import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "../../../../auth/[...nextauth]/route";
import { Redis } from "@upstash/redis";
import { User } from "@/types/user";

// Initialize Upstash Redis Client
const redis = new Redis({
  url: process.env.UPSTASH_REDIS_URL!,
  token: process.env.UPSTASH_REDIS_TOKEN!,
});

interface RoleUpdateParams {
  params: {
    userId: string;
  };
}

export async function PUT(req: Request, { params }: RoleUpdateParams) {
  const session = await getServerSession(authOptions);

  if (!session || (session.user as any).role !== "admin") {
    return NextResponse.json(
      { message: "Forbidden: Administrator access required." },
      { status: 403 }
    );
  }

  const { userId } = params;
  if (!userId) {
    return NextResponse.json(
      { message: "User ID is required." },
      { status: 400 }
    );
  }

  try {
    const body = await req.json();
    const { role } = body;

    // Validate the role
    if (!role || (role !== "admin" && role !== "user")) {
      return NextResponse.json(
        { message: 'Invalid role specified. Must be "admin" or "user".' },
        { status: 400 }
      );
    }

    const userKey = `user:${userId}`;
    const user = await redis.get<User>(userKey);

    if (!user) {
      return NextResponse.json({ message: "User not found." }, { status: 404 });
    }

    // Update the user's role
    const updatedUser: User = {
      ...user,
      role: role,
      updatedAt: new Date(),
    };

    // Save the updated user object back to Redis
    await redis.set(userKey, JSON.stringify(updatedUser));

    const { hashedPassword, ...userWithoutPassword } = updatedUser;
    return NextResponse.json(
      { message: "User role updated successfully.", user: userWithoutPassword },
      { status: 200 }
    );
  } catch (error) {
    console.error(`Error updating role for user ${userId}:`, error);
    if (error instanceof SyntaxError) {
      // Handle JSON parsing errors
      return NextResponse.json(
        { message: "Invalid JSON in request body." },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { message: "Error updating user role.", error: (error as Error).message },
      { status: 500 }
    );
  }
}
