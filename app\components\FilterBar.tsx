"use client";

import React, { useState, useEffect } from "react";
import { ProductFilter } from "../types/index";
import { useLanguage } from "../context/LanguageContext";
import { useCategories } from "../utils/api-client";

interface FilterBarProps {
  filters: ProductFilter;
  onFilterChange: (filters: ProductFilter) => void;
}

export default function FilterBar({ filters, onFilterChange }: FilterBarProps) {
  const { t } = useLanguage();
  const [searchInput, setSearchInput] = useState(filters.search || "");
  const { categories: apiCategories, isLoading: categoriesLoading } =
    useCategories();

  useEffect(() => {
    const handler = setTimeout(() => {
      onFilterChange({ ...filters, search: searchInput });
    }, 500);
    return () => clearTimeout(handler);
  }, [searchInput, filters, onFilterChange]);

  const formattedCategories = [
    { value: "", label: t("filters.categories.all") },
    ...(apiCategories || []).map((category: any) => ({
      value: category.id.toString(),
      label: category.name,
    })),
  ];

  return (
    <div className="bg-white p-4 rounded-lg shadow">
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-4">
        <div className="relative">
          <input
            type="text"
            placeholder={t("filters.search.placeholder")}
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
            className="border rounded p-2 pr-10 w-full"
          />
          {searchInput && (
            <button
              onClick={() => setSearchInput("")}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
              aria-label="Clear search"
              type="button"
            >
              &#x2715;
            </button>
          )}
        </div>

        <select
          value={filters.category || ""}
          onChange={(e) =>
            onFilterChange({ ...filters, category: e.target.value })
          }
          className="border rounded p-2"
          disabled={categoriesLoading}
        >
          {categoriesLoading ? (
            <option value="">{t("filters.categories.loading")}</option>
          ) : (
            formattedCategories.map((cat) => (
              <option key={cat.value} value={cat.value}>
                {cat.label}
              </option>
            ))
          )}
        </select>

        <select
          value={filters.stockStatus || "all"}
          onChange={(e) =>
            onFilterChange({
              ...filters,
              stockStatus: e.target.value as
                | "all"
                | "instock"
                | "outofstock"
                | "onbackorder",
            })
          }
          className="border rounded p-2"
        >
          <option value="all">{t("filters.stockStatus.all")}</option>
          <option value="instock">{t("filters.stockStatus.inStock")}</option>
          <option value="outofstock">
            {t("filters.stockStatus.outOfStock")}
          </option>
          <option value="onbackorder">
            {t("filters.stockStatus.onBackorder")}
          </option>
        </select>

        <button
          type="button"
          onClick={() =>
            onFilterChange({
              search: "",
              category: "",
              stockStatus: "all",
            })
          }
          className="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300"
        >
          {t("filters.reset")}
        </button>
      </div>
    </div>
  );
}
