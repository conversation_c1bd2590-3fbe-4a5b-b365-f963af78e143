import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "../../auth/[...nextauth]/route";
import { Redis } from "@upstash/redis";
import { User } from "@/types/user";

// Initialize Upstash Redis Client
const redis = new Redis({
  url: process.env.UPSTASH_REDIS_URL!,
  token: process.env.UPSTASH_REDIS_TOKEN!,
});

export async function GET(req: Request) {
  const session = await getServerSession(authOptions);

  if (!session || (session.user as any).role !== "admin") {
    return NextResponse.json(
      { message: "Forbidden: Administrator access required." },
      { status: 403 }
    );
  }

  try {
    const users: User[] = [];
    let cursor = "0";

    do {
      // Scan for keys matching the user pattern. Adjust count as needed.
      // We are looking for keys like 'user:<uuid>'
      const [nextCursor, keys] = await redis.scan(cursor, {
        match: "user:*",
        count: 100,
      });
      cursor = nextCursor;

      const userKeys = keys.filter(
        (key) =>
          key.startsWith("user:") &&
          !key.includes(":email:") &&
          !key.includes(":session:") && // Example, if adapter stores sessions like this
          key.split(":").length === 2 // Ensure it's user:<id> and not user:email:<EMAIL>
      );

      if (userKeys.length > 0) {
        const pipeline = redis.pipeline();
        userKeys.forEach((key) => pipeline.get<User>(key));
        const results = await pipeline.exec<User[]>();

        results.forEach((user) => {
          if (user) {
            // User might be null if a key was deleted between SCAN and GET
            const { hashedPassword, ...userWithoutPassword } = user;
            users.push(userWithoutPassword as User); // Cast because TS doesn't know hashedPassword is removed
          }
        });
      }
    } while (cursor !== "0");

    return NextResponse.json(users, { status: 200 });
  } catch (error) {
    console.error("Error fetching users:", error);
    return NextResponse.json(
      { message: "Error fetching users", error: (error as Error).message },
      { status: 500 }
    );
  }
}

// POST handler for creating users could be added here if needed,
// but the subtask mentions signup already covers creation.
// For now, only GET is implemented as per the primary requirement.
