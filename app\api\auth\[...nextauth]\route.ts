import NextAuth, { AuthOptions } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import { UpstashRedisAdapter } from "@next-auth/upstash-redis-adapter";
import { Redis } from "@upstash/redis";
import bcrypt from "bcrypt";
import { User } from "@/types/user";

// Initialize Upstash Redis Client
const redis = new Redis({
  url: process.env.UPSTASH_REDIS_URL!,
  token: process.env.UPSTASH_REDIS_TOKEN!,
});

export const authOptions: AuthOptions = {
  adapter: UpstashRedisAdapter(redis),
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("Missing credentials");
        }

        // Attempt to get user ID from email mapping
        const userIdKey = `user:email:${credentials.email}`;
        const userId = await redis.get<string>(userIdKey);

        if (!userId) {
          // Fallback: try to get user by email directly if adapter stores it this way
          // This is less likely for UpstashRedisAdapter, which normalizes by ID.
          // const userByEmail = await (redis.get<User>)(`user:email:${credentials.email}`); // Or adapter.getUserByEmail
          // For now, we'll rely on the primary lookup method (user:email:<email> -> user_id)
          console.error(`User ID not found for email: ${credentials.email}`);
          throw new Error("User not found.");
        }

        const userKey = `user:${userId}`;
        const user = await redis.get<User>(userKey);

        if (!user || !user.hashedPassword) {
          // If user is null here, it means userId was found but the user object itself is missing.
          console.error(
            `User object not found for ID: ${userId}, despite email mapping.`
          );
          throw new Error("User data not found or password not set.");
        }

        const isValidPassword = await bcrypt.compare(
          credentials.password,
          user.hashedPassword
        );

        if (!isValidPassword) {
          throw new Error("Invalid password");
        }

        return {
          // Return the user object expected by NextAuth.js
          id: user.id,
          email: user.email,
          name: user.name ?? null, // Ensure name is null if undefined
          image: user.image ?? null, // Ensure image is null if undefined
          role: user.role,
        };
      },
    }),
  ],
  session: { strategy: "jwt" },
  pages: { signIn: "/login" },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        // User object is available on sign-in
        token.id = user.id;
        // The User object from authorize might not directly have 'role',
        // or NextAuth's internal User type might not.
        // We ensure 'role' from our 'authorize' returned user object is correctly passed to the token.
        token.role = (user as any).role;
      }
      return token;
    },
    async session({ session, token }) {
      if (session.user) {
        session.user.id = token.id as string;
        session.user.role = token.role as string; // Add role to session user
      }
      return session;
    },
  },
  secret: process.env.NEXTAUTH_SECRET,
  debug: process.env.NODE_ENV === "development",
};

const handler = NextAuth(authOptions);
export { handler as GET, handler as POST };
