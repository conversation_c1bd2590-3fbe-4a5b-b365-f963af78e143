<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create First Admin User</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 500px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background-color: #007cba;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
        }
        button:hover {
            background-color: #005a87;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .message {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Create First Admin User</h1>
        
        <div class="warning">
            <strong>⚠️ Important:</strong> This page should only be used once to create the first admin user. 
            Remove this file and the API endpoint after use for security.
        </div>

        <form id="adminForm">
            <div class="form-group">
                <label for="name">Full Name:</label>
                <input type="text" id="name" name="name" required>
            </div>

            <div class="form-group">
                <label for="email">Email Address:</label>
                <input type="email" id="email" name="email" required>
            </div>

            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" required minlength="6">
            </div>

            <div class="form-group">
                <label for="adminSecret">Admin Creation Secret:</label>
                <input type="password" id="adminSecret" name="adminSecret" required 
                       placeholder="Enter the ADMIN_CREATION_SECRET from environment variables">
            </div>

            <button type="submit" id="submitBtn">Create Admin User</button>
        </form>

        <div id="message"></div>
    </div>

    <script>
        document.getElementById('adminForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const messageDiv = document.getElementById('message');
            
            submitBtn.disabled = true;
            submitBtn.textContent = 'Creating...';
            messageDiv.innerHTML = '';

            const formData = {
                name: document.getElementById('name').value,
                email: document.getElementById('email').value,
                password: document.getElementById('password').value,
                adminSecret: document.getElementById('adminSecret').value
            };

            try {
                const response = await fetch('/api/admin/create-first-admin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();

                if (response.ok) {
                    messageDiv.innerHTML = `
                        <div class="message success">
                            <strong>✅ Success!</strong><br>
                            Admin user created successfully!<br>
                            Email: ${result.user.email}<br>
                            <br>
                            <strong>Next steps:</strong><br>
                            1. Delete this HTML file<br>
                            2. Remove the API endpoint<br>
                            3. Login with your new admin account
                        </div>
                    `;
                    document.getElementById('adminForm').style.display = 'none';
                } else {
                    messageDiv.innerHTML = `
                        <div class="message error">
                            <strong>❌ Error:</strong> ${result.message}
                        </div>
                    `;
                }
            } catch (error) {
                messageDiv.innerHTML = `
                    <div class="message error">
                        <strong>❌ Error:</strong> Failed to create admin user. Check console for details.
                    </div>
                `;
                console.error('Error:', error);
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = 'Create Admin User';
            }
        });
    </script>
</body>
</html>
