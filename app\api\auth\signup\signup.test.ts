import { POST } from "./route";
import bcrypt from "bcrypt";
import { NextResponse } from "next/server";

// Mock Prisma
const mockPrismaUserFindUnique = jest.fn();
const mockPrismaUserCreate = jest.fn();

jest.mock("@/lib/db", () => ({
  prisma: {
    user: {
      findUnique: mockPrismaUserFindUnique,
      create: mockPrismaUserCreate,
    },
  },
}));

// Mock bcrypt
const mockBcryptHash = jest.fn();
jest.mock("bcryptjs", () => ({
  hash: mockBcryptHash,
  compare: jest.fn(),
}));

describe("POST /api/auth/signup", () => {
  beforeEach(() => {
    // Clear all mock implementations and call history before each test
    mockPrismaUserFindUnique.mockReset();
    mockPrismaUserCreate.mockReset();
    mockBcryptHash.mockReset();

    // Default mock implementations
    mockBcryptHash.mockResolvedValue("hashedTestPassword");
  });

  it("should successfully sign up a new user", async () => {
    mockPrismaUserFindUnique.mockResolvedValue(null); // User does not exist
    const mockCreatedUser = {
      id: "test-uuid-123",
      name: "Test User",
      email: "<EMAIL>",
      hashedPassword: "hashedTestPassword",
      role: "user",
      emailVerified: null,
      image: null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    mockPrismaUserCreate.mockResolvedValue(mockCreatedUser);

    const requestBody = {
      name: "Test User",
      email: "<EMAIL>",
      password: "password123",
    };
    const req = new Request("http://localhost/api/auth/signup", {
      method: "POST",
      body: JSON.stringify(requestBody),
      headers: { "Content-Type": "application/json" },
    });

    const response = await POST(req);
    const responseBody = await response.json();

    expect(response.status).toBe(201);
    expect(responseBody.message).toBe("User created successfully");
    expect(responseBody.user).toBeDefined();
    expect(responseBody.user.email).toBe(requestBody.email);
    expect(responseBody.user.name).toBe(requestBody.name);
    expect(responseBody.user.id).toBe("test-uuid-123");
    expect(responseBody.user.hashedPassword).toBeUndefined(); // Ensure password is not returned

    expect(mockBcryptHash).toHaveBeenCalledWith(requestBody.password, 10);
    expect(mockPrismaUserFindUnique).toHaveBeenCalledWith({
      where: { email: requestBody.email },
    });
    expect(mockPrismaUserCreate).toHaveBeenCalledWith({
      data: {
        name: requestBody.name,
        email: requestBody.email,
        hashedPassword: "hashedTestPassword",
        role: "user",
      },
    });
  });

  it("should return 409 if user already exists", async () => {
    const existingUser = {
      id: "existing-user-id",
      name: "Existing User",
      email: "<EMAIL>",
      hashedPassword: "hashedPassword",
      role: "user",
      emailVerified: null,
      image: null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    mockPrismaUserFindUnique.mockResolvedValue(existingUser); // User exists

    const requestBody = {
      name: "Test User",
      email: "<EMAIL>",
      password: "password123",
    };
    const req = new Request("http://localhost/api/auth/signup", {
      method: "POST",
      body: JSON.stringify(requestBody),
      headers: { "Content-Type": "application/json" },
    });

    const response = await POST(req);
    const responseBody = await response.json();

    expect(response.status).toBe(409);
    expect(responseBody.message).toBe("User already exists with this email");
  });

  it("should return 400 for missing email", async () => {
    const requestBody = { name: "Test User", password: "password123" }; // Missing email
    const req = new Request("http://localhost/api/auth/signup", {
      method: "POST",
      body: JSON.stringify(requestBody),
      headers: { "Content-Type": "application/json" },
    });

    const response = await POST(req);
    const responseBody = await response.json();

    expect(response.status).toBe(400);
    expect(responseBody.message).toBe("Email and password are required");
  });

  it("should return 400 for missing password", async () => {
    const requestBody = { name: "Test User", email: "<EMAIL>" }; // Missing password
    const req = new Request("http://localhost/api/auth/signup", {
      method: "POST",
      body: JSON.stringify(requestBody),
      headers: { "Content-Type": "application/json" },
    });

    const response = await POST(req);
    const responseBody = await response.json();

    expect(response.status).toBe(400);
    expect(responseBody.message).toBe("Email and password are required");
  });

  it("should return 400 for missing name", async () => {
    const requestBody = { email: "<EMAIL>", password: "password123" }; // Missing name
    const req = new Request("http://localhost/api/auth/signup", {
      method: "POST",
      body: JSON.stringify(requestBody),
      headers: { "Content-Type": "application/json" },
    });

    const response = await POST(req);
    const responseBody = await response.json();

    expect(response.status).toBe(400);
    expect(responseBody.message).toBe("Name is required");
  });
});
