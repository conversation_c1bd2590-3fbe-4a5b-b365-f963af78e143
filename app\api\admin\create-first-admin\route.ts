import { NextResponse } from "next/server";
import { prisma } from "@/lib/db";
import bcrypt from "bcrypt";

// IMPORTANT: This endpoint should be removed after creating the first admin
export async function POST(request: Request) {
  try {
    // Check for admin creation secret
    const { email, password, name, adminSecret } = await request.json();

    if (adminSecret !== process.env.ADMIN_CREATION_SECRET) {
      return NextResponse.json(
        { message: "Unauthorized: Invalid admin secret" },
        { status: 401 }
      );
    }

    // Check if any admin already exists
    const existingAdmin = await prisma.user.findFirst({
      where: { role: "admin" },
    });

    if (existingAdmin) {
      return NextResponse.json(
        { message: "Admin user already exists" },
        { status: 409 }
      );
    }

    if (!email || !password || !name) {
      return NextResponse.json(
        { message: "Email, password, and name are required" },
        { status: 400 }
      );
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create admin user
    const adminUser = await prisma.user.create({
      data: {
        name,
        email,
        hashedPassword,
        role: "admin",
        preferences: JSON.stringify({ itemsPerPage: 10 }),
      },
    });

    console.log("Admin user created:", adminUser.email);

    return NextResponse.json(
      {
        message: "Admin user created successfully",
        user: {
          id: adminUser.id,
          email: adminUser.email,
          name: adminUser.name,
          role: adminUser.role,
        },
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating admin user:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
