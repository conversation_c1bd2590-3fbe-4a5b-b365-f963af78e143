import { PUT } from './route'; // Adjust to your actual PUT handler path
import { Redis } from '@upstash/redis';
import { getServerSession } from 'next-auth/next';
import { NextResponse } from 'next/server';

// Mock Upstash Redis
const mockRedisGet = jest.fn();
const mockRedisSet = jest.fn();
jest.mock('@upstash/redis', () => {
  return {
    Redis: jest.fn(() => ({
      get: mockRedisGet,
      set: mockRedisSet,
    })),
  };
});

// Mock next-auth getServerSession
const mockGetServerSession = jest.fn();
jest.mock('next-auth/next', () => ({
  getServerSession: jest.fn((...args) => mockGetServerSession(...args)),
}));

describe('PUT /api/admin/users/[userId]/role', () => {
  beforeEach(() => {
    mockGetServerSession.mockReset();
    mockRedisGet.mockReset();
    mockRedisSet.mockReset();
  });

  const mockUser = {
    id: 'user123',
    name: 'Test User',
    email: '<EMAIL>',
    role: 'user',
    hashedPassword: 'hashedpassword',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockAdminUser = {
    id: 'admin456',
    name: 'Admin User',
    email: '<EMAIL>',
    role: 'admin',
    hashedPassword: 'hashedpassword',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  it('should return 403 if user is not authenticated', async () => {
    mockGetServerSession.mockResolvedValue(null);
    const req = new Request('http://localhost/api/admin/users/user123/role', {
      method: 'PUT',
      body: JSON.stringify({ role: 'admin' }),
    });
    const response = await PUT(req, { params: { userId: 'user123' } });
    const responseBody = await response.json();
    expect(response.status).toBe(403);
    expect(responseBody.message).toBe('Forbidden: Administrator access required.');
  });

  it('should return 403 if user is not an admin', async () => {
    mockGetServerSession.mockResolvedValue({ user: { ...mockUser, role: 'user' } });
    const req = new Request('http://localhost/api/admin/users/user123/role', {
      method: 'PUT',
      body: JSON.stringify({ role: 'admin' }),
    });
    const response = await PUT(req, { params: { userId: 'user123' } });
    const responseBody = await response.json();
    expect(response.status).toBe(403);
    expect(responseBody.message).toBe('Forbidden: Administrator access required.');
  });

  it('should successfully update user role for an admin', async () => {
    mockGetServerSession.mockResolvedValue({ user: { ...mockAdminUser } });
    mockRedisGet.mockResolvedValue(mockUser); // Target user exists
    mockRedisSet.mockResolvedValue('OK');

    const newRole = 'admin';
    const req = new Request(`http://localhost/api/admin/users/${mockUser.id}/role`, {
      method: 'PUT',
      body: JSON.stringify({ role: newRole }),
      headers: { 'Content-Type': 'application/json' },
    });

    const response = await PUT(req, { params: { userId: mockUser.id } });
    const responseBody = await response.json();

    expect(response.status).toBe(200);
    expect(responseBody.message).toBe('User role updated successfully.');
    expect(responseBody.user.role).toBe(newRole);
    expect(responseBody.user.id).toBe(mockUser.id);
    expect(responseBody.user.hashedPassword).toBeUndefined();

    expect(mockRedisGet).toHaveBeenCalledWith(`user:${mockUser.id}`);
    expect(mockRedisSet).toHaveBeenCalledWith(
      `user:${mockUser.id}`,
      expect.stringContaining(`"role":"${newRole}"`) // Check if role is updated in stringified JSON
    );
  });

  it('should return 404 if user to update is not found', async () => {
    mockGetServerSession.mockResolvedValue({ user: { ...mockAdminUser } });
    mockRedisGet.mockResolvedValue(null); // User not found

    const req = new Request('http://localhost/api/admin/users/nonexistentuser/role', {
      method: 'PUT',
      body: JSON.stringify({ role: 'admin' }),
      headers: { 'Content-Type': 'application/json' },
    });

    const response = await PUT(req, { params: { userId: 'nonexistentuser' } });
    const responseBody = await response.json();

    expect(response.status).toBe(404);
    expect(responseBody.message).toBe('User not found.');
  });

  it('should return 400 for invalid role', async () => {
    mockGetServerSession.mockResolvedValue({ user: { ...mockAdminUser } });
    mockRedisGet.mockResolvedValue(mockUser);

    const req = new Request(`http://localhost/api/admin/users/${mockUser.id}/role`, {
      method: 'PUT',
      body: JSON.stringify({ role: 'invalidrole' }), // Invalid role
      headers: { 'Content-Type': 'application/json' },
    });

    const response = await PUT(req, { params: { userId: mockUser.id } });
    const responseBody = await response.json();

    expect(response.status).toBe(400);
    expect(responseBody.message).toBe('Invalid role specified. Must be "admin" or "user".');
  });
  
  it('should return 400 if userId is missing from params', async () => {
    mockGetServerSession.mockResolvedValue({ user: { ...mockAdminUser } });
    const req = new Request(`http://localhost/api/admin/users//role`, { // Missing userId
      method: 'PUT',
      body: JSON.stringify({ role: 'admin' }),
      headers: { 'Content-Type': 'application/json' },
    });
    // Simulate Next.js not providing userId in params due to route structure
    const response = await PUT(req, { params: { userId: '' } }); 
    const responseBody = await response.json();

    expect(response.status).toBe(400);
    expect(responseBody.message).toBe('User ID is required.');
  });
});
