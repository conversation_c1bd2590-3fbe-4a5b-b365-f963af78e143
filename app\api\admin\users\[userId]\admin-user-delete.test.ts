import { DELETE } from './route'; // Adjust to your actual DELETE handler path
import { Redis } from '@upstash/redis';
import { getServerSession } from 'next-auth/next';
import { NextResponse } from 'next/server';

// Mock Upstash Redis
const mockRedisGet = jest.fn();
const mockRedisLRange = jest.fn();
const mockRedisMulti = jest.fn().mockReturnThis(); // For chaining
const mockRedisDel = jest.fn(); // For individual del calls within multi
const mockRedisExec = jest.fn();

jest.mock('@upstash/redis', () => {
  return {
    Redis: jest.fn(() => ({
      get: mockRedisGet,
      lrange: mockRedisLRange,
      multi: jest.fn(() => ({ // Mock the pipeline object itself
        del: mockRedisDel,
        exec: mockRedisExec,
      })),
    })),
  };
});

// Mock next-auth getServerSession
const mockGetServerSession = jest.fn();
jest.mock('next-auth/next', () => ({
  getServerSession: jest.fn((...args) => mockGetServerSession(...args)),
}));

describe('DELETE /api/admin/users/[userId]', () => {
  beforeEach(() => {
    mockGetServerSession.mockReset();
    mockRedisGet.mockReset();
    mockRedisLRange.mockReset();
    mockRedisDel.mockReset();
    mockRedisExec.mockReset();
  });

  const mockTargetUser = {
    id: 'userToDelete123',
    name: 'User To Delete',
    email: '<EMAIL>',
    role: 'user',
    hashedPassword: 'hashedpassword',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockAdminUser = {
    id: 'admin456',
    name: 'Admin User',
    email: '<EMAIL>',
    role: 'admin',
  };

  it('should return 403 if user is not authenticated', async () => {
    mockGetServerSession.mockResolvedValue(null);
    const req = new Request(`http://localhost/api/admin/users/${mockTargetUser.id}`, { method: 'DELETE' });
    const response = await DELETE(req, { params: { userId: mockTargetUser.id } });
    const responseBody = await response.json();
    expect(response.status).toBe(403);
    expect(responseBody.message).toBe('Forbidden: Administrator access required.');
  });

  it('should return 403 if user is not an admin', async () => {
    mockGetServerSession.mockResolvedValue({ user: { ...mockTargetUser, role: 'user' } }); // Non-admin
    const req = new Request(`http://localhost/api/admin/users/${mockTargetUser.id}`, { method: 'DELETE' });
    const response = await DELETE(req, { params: { userId: mockTargetUser.id } });
    const responseBody = await response.json();
    expect(response.status).toBe(403);
    expect(responseBody.message).toBe('Forbidden: Administrator access required.');
  });

  it('should successfully delete a user for an admin', async () => {
    mockGetServerSession.mockResolvedValue({ user: { ...mockAdminUser } });
    mockRedisGet.mockResolvedValue(mockTargetUser); // User to delete exists
    mockRedisLRange.mockResolvedValue(['sessionToken1', 'sessionToken2']); // User has active sessions
    mockRedisExec.mockResolvedValue(['OK', 'OK', 'OK', 'OK', 'OK']); // Simulate successful transaction

    const req = new Request(`http://localhost/api/admin/users/${mockTargetUser.id}`, { method: 'DELETE' });
    const response = await DELETE(req, { params: { userId: mockTargetUser.id } });
    const responseBody = await response.json();

    expect(response.status).toBe(200);
    expect(responseBody.message).toBe(`User ${mockTargetUser.id} and associated data deleted successfully.`);

    expect(mockRedisGet).toHaveBeenCalledWith(`user:${mockTargetUser.id}`);
    expect(mockRedisLRange).toHaveBeenCalledWith(`user:session:${mockTargetUser.id}`, 0, -1);
    
    expect(mockRedisDel).toHaveBeenCalledWith(`user:${mockTargetUser.id}`);
    expect(mockRedisDel).toHaveBeenCalledWith(`user:email:${mockTargetUser.email}`);
    expect(mockRedisDel).toHaveBeenCalledWith('session:sessionToken1', 'session:sessionToken2');
    expect(mockRedisDel).toHaveBeenCalledWith(`user:session:${mockTargetUser.id}`);
    expect(mockRedisExec).toHaveBeenCalledTimes(1);
  });

  it('should return 404 if user to delete is not found', async () => {
    mockGetServerSession.mockResolvedValue({ user: { ...mockAdminUser } });
    mockRedisGet.mockResolvedValue(null); // User not found

    const req = new Request('http://localhost/api/admin/users/nonexistentuser', { method: 'DELETE' });
    const response = await DELETE(req, { params: { userId: 'nonexistentuser' } });
    const responseBody = await response.json();

    expect(response.status).toBe(404);
    expect(responseBody.message).toBe('User not found.');
  });
  
  it('should return 400 if userId is missing from params', async () => {
    mockGetServerSession.mockResolvedValue({ user: { ...mockAdminUser } });
    const req = new Request('http://localhost/api/admin/users/', { method: 'DELETE' }); // Missing userId
    const response = await DELETE(req, { params: { userId: '' } });
    const responseBody = await response.json();

    expect(response.status).toBe(400);
    expect(responseBody.message).toBe('User ID is required.');
  });

  // Optional: Test if admin tries to delete themselves.
  // The current DELETE handler in app/api/admin/users/[userId]/route.ts does not prevent self-deletion.
  // If it did, a test case like the following would be appropriate:
  /*
  it('should prevent an admin from deleting themselves', async () => {
    mockGetServerSession.mockResolvedValue({ user: { ...mockAdminUser, id: 'adminSelfDeleteId' } });
    mockRedisGet.mockResolvedValue({ ...mockAdminUser, id: 'adminSelfDeleteId', email: '<EMAIL>' });

    const req = new Request(`http://localhost/api/admin/users/adminSelfDeleteId`, { method: 'DELETE' });
    const response = await DELETE(req, { params: { userId: 'adminSelfDeleteId' } });
    const responseBody = await response.json();

    // This depends on the specific error message and status code implemented for self-deletion
    expect(response.status).toBe(400); // Or 403
    expect(responseBody.message).toContain('Cannot delete your own account');
    expect(mockRedisExec).not.toHaveBeenCalled();
  });
  */
});
