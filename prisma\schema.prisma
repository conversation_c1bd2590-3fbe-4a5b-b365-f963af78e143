// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  binaryTargets = ["native", "rhel-openssl-1.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

// NextAuth.js Models
// NOTE: When using postgresql, mysql or sqlserver,
// uncomment the @db.Text annotations below
// @db.Text

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? // @db.Text
  access_token      String? // @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? // @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  hashedPassword String?
  role          String    @default("user")
  preferences   String?   @default("{\"itemsPerPage\":10}") // JSON string for user preferences
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  accounts      Account[]
  sessions      Session[]
  invitations   Invitation[] // Invitations created by this user
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Invitation {
  id        String   @id @default(cuid())
  email     String   @unique
  token     String   @unique
  role      String   @default("user")
  createdBy String
  createdAt DateTime @default(now())
  expiresAt DateTime
  usedAt    DateTime?
  isUsed    Boolean  @default(false)

  // Relation to the admin who created the invitation
  creator   User     @relation(fields: [createdBy], references: [id], onDelete: Cascade)
}
