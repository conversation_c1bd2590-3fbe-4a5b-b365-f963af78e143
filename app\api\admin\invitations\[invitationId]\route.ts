import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "../../../auth/[...nextauth]/route";
import { prismaAuth } from "@/lib/db";

// DELETE - Revoke invitation
export async function DELETE(
  request: Request,
  { params }: { params: { invitationId: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || session.user.role !== "admin") {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const { invitationId } = params;

    // Check if invitation exists
    const invitation = await prismaAuth.invitation.findUnique({
      where: { id: invitationId },
    });

    if (!invitation) {
      return NextResponse.json(
        { message: "Invitation not found" },
        { status: 404 }
      );
    }

    if (invitation.isUsed) {
      return NextResponse.json(
        { message: "Cannot delete used invitation" },
        { status: 400 }
      );
    }

    // Delete invitation
    await prismaAuth.invitation.delete({
      where: { id: invitationId },
    });

    return NextResponse.json(
      { message: "Invitation revoked successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error deleting invitation:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
