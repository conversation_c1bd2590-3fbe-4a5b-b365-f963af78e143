import { Product, ProductsResponse, ProductFilter } from "../types/index"; // Added ProductFilter import
import { useQuery } from "@tanstack/react-query";

const fetcher = async (url: string) => {
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error("Failed to fetch data"); // Generic error message
  }
  return response.json();
};

// useProducts hook might need adjustment if filters are applied server-side by default
export function useProducts() {
  const { data, error, isLoading } = useQuery<Product[]>({
    queryKey: ["products"], // Consider adding filters to queryKey if they affect this hook's usage
    queryFn: () => fetcher("/api/products"), // This fetches without filters
    staleTime: 300000, // 5 minutes
    gcTime: 3600000, // 1 hour
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
  });

  return {
    products: data,
    isLoading,
    error,
  };
}

// Updated FetchProductsParams to include filter fields
interface FetchProductsParams {
  search?: string;
  page?: number;
  per_page?: number;
  stockStatus?: ProductFilter["stockStatus"];
  category?: string; // Added category parameter
}

export async function fetchProducts({
  search = "",
  page = 1,
  per_page = 10,
  stockStatus,
  category,
}: FetchProductsParams): Promise<ProductsResponse> {
  const queryParams = new URLSearchParams({
    page: page.toString(),
    per_page: per_page.toString(),
  });

  if (search) {
    queryParams.append("search", search);
  }

  if (stockStatus && stockStatus !== "all") {
    queryParams.append("stock_status", stockStatus);
  }

  if (category) {
    queryParams.append("category", category);
  }

  const response = await fetch(`/api/products?${queryParams.toString()}`);

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `API error: ${response.statusText}`);
  }

  return response.json();
}

export async function updateProduct(
  productId: number,
  data: Partial<Product>
): Promise<Product> {
  try {
    // If updating stock, ensure stock management is enabled
    if ("stock_quantity" in data) {
      data = {
        ...data,
        manage_stock: true,
        // stock_status is often automatically handled by WC based on quantity
        // but explicitly setting might be needed depending on WC settings
        // stock_status:
        //   data.stock_quantity && data.stock_quantity > 0
        //     ? "instock"
        //     : "outofstock",
      };
    }

    const response = await fetch(`/api/products/${productId}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        errorData.message || `Failed to update product: ${response.statusText}`
      );
    }

    return await response.json();
  } catch (error) {
    console.error("Error updating product:", error);
    throw error;
  }
}

// New function to fetch categories
export async function fetchCategories() {
  const response = await fetch("/api/categories");
  if (!response.ok) {
    throw new Error(`API error: ${response.statusText}`);
  }
  return response.json();
}

// Function to use categories with React Query
export function useCategories() {
  const { data, error, isLoading } = useQuery({
    queryKey: ["categories"],
    queryFn: fetchCategories,
    staleTime: 600000, // 10 minutes
    gcTime: 3600000, // 1 hour
    refetchOnWindowFocus: false,
  });

  return {
    categories: data || [],
    isLoading,
    error,
  };
}
