import { NextResponse } from "next/server";
import { prismaAuth } from "@/lib/db";

// POST - Validate invitation token
export async function POST(request: Request) {
  try {
    const { token } = await request.json();

    if (!token) {
      return NextResponse.json(
        { message: "Token is required" },
        { status: 400 }
      );
    }

    // Find invitation by token
    const invitation = await prismaAuth.invitation.findUnique({
      where: { token },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!invitation) {
      return NextResponse.json(
        { message: "Invalid invitation token" },
        { status: 404 }
      );
    }

    if (invitation.isUsed) {
      return NextResponse.json(
        { message: "Invitation has already been used" },
        { status: 400 }
      );
    }

    if (new Date() > invitation.expiresAt) {
      return NextResponse.json(
        { message: "Invitation has expired" },
        { status: 400 }
      );
    }

    // Check if user already exists
    const existingUser = await prismaAuth.user.findUnique({
      where: { email: invitation.email },
    });

    if (existingUser) {
      return NextResponse.json(
        { message: "User with this email already exists" },
        { status: 409 }
      );
    }

    return NextResponse.json(
      {
        valid: true,
        invitation: {
          id: invitation.id,
          email: invitation.email,
          role: invitation.role,
          createdBy: invitation.creator.name,
          expiresAt: invitation.expiresAt,
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error validating invitation:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
