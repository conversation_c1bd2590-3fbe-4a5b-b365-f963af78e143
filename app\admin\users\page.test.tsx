import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import AdminUsersPage from './page'; // Adjust path to your page component
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation'; // useRouter is not directly used in AdminUsersPage from provided code
import { User } from '../../../types/user'; // Adjust path as necessary

// Mock next-auth/react
jest.mock('next-auth/react', () => ({
  useSession: jest.fn(),
}));

// Mock next/navigation (if page directly uses it, though not in provided code)
// const mockRouterPush = jest.fn();
// jest.mock('next/navigation', () => ({
//   useRouter: jest.fn(() => ({
//     push: mockRouterPush,
//     prefetch: jest.fn(),
//   })),
// }));

// Mock global fetch
global.fetch = jest.fn();

const mockUsers: User[] = [
  { id: '1', name: '<PERSON>', email: '<EMAIL>', role: 'user', createdAt: new Date(), updatedAt: new Date(), hashedPassword: 'p1', emailVerified: new Date() },
  { id: '2', name: 'Bob Johnson', email: '<EMAIL>', role: 'admin', createdAt: new Date(), updatedAt: new Date(), hashedPassword: 'p2' },
  { id: '3', name: 'Charlie Brown', email: '<EMAIL>', role: 'user', createdAt: new Date(), updatedAt: new Date(), hashedPassword: 'p3' },
];

describe('AdminUsersPage', () => {
  beforeEach(() => {
    (useSession as jest.Mock).mockClear();
    (global.fetch as jest.Mock).mockClear();
    // mockRouterPush.mockClear();
  });

  const renderAdminPage = () => render(<AdminUsersPage />);

  describe('Admin Access', () => {
    beforeEach(() => {
      (useSession as jest.Mock).mockReturnValue({
        data: { user: { id: 'admin007', role: 'admin', name: 'Admin User' } },
        status: 'authenticated',
      });
    });

    it('renders page with users when user is admin', async () => {
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockUsers,
      });
      renderAdminPage();
      expect(await screen.findByText('User Management')).toBeInTheDocument();
      expect(screen.getByText('Loading user data...')).toBeInTheDocument(); // Initial loading
      
      await waitFor(() => expect(screen.queryByText('Loading user data...')).not.toBeInTheDocument());
      
      expect(screen.getByText('Alice Smith')).toBeInTheDocument();
      expect(screen.getByText('Bob Johnson')).toBeInTheDocument();
      expect(screen.getAllByRole('button', { name: /edit role/i })).toHaveLength(mockUsers.length);
      expect(screen.getAllByRole('button', { name: /delete/i })).toHaveLength(mockUsers.length);
    });

    it('handles role change interaction', async () => {
      (global.fetch as jest.Mock)
        .mockResolvedValueOnce({ ok: true, json: async () => mockUsers }) // Initial fetch
        .mockResolvedValueOnce({ ok: true, json: async () => ({ message: 'Role updated' }) }); // PUT request for role change
      
      renderAdminPage();
      await waitFor(() => expect(screen.queryByText('Loading user data...')).not.toBeInTheDocument());

      // Find the "Edit Role" button for Alice Smith (first user)
      const editRoleButtons = screen.getAllByRole('button', { name: /edit role/i });
      fireEvent.click(editRoleButtons[0]);

      // Select new role
      const roleSelect = screen.getByRole('combobox'); // The select element
      fireEvent.change(roleSelect, { target: { value: 'admin' } });
      expect((roleSelect as HTMLSelectElement).value).toBe('admin');
      
      // Click Save
      const saveButton = screen.getByRole('button', { name: /save/i });
      fireEvent.click(saveButton);

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith(`/api/admin/users/${mockUsers[0].id}/role`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ role: 'admin' }),
        });
      });
      // Add assertion for users list refresh (fetch called again)
      await waitFor(() => expect(global.fetch).toHaveBeenCalledTimes(3)); // Initial, PUT, Refresh
    });

    it('handles delete user interaction', async () => {
      window.confirm = jest.fn(() => true); // Mock window.confirm

      (global.fetch as jest.Mock)
        .mockResolvedValueOnce({ ok: true, json: async () => mockUsers }) // Initial fetch
        .mockResolvedValueOnce({ ok: true, json: async () => ({ message: 'User deleted' }) }); // DELETE request

      renderAdminPage();
      await waitFor(() => expect(screen.queryByText('Loading user data...')).not.toBeInTheDocument());

      const deleteButtons = screen.getAllByRole('button', { name: /delete/i });
      // Let's try to delete Bob Johnson (second user, who is an admin)
      // The delete button for the current admin (admin007) might be disabled if they are in the list.
      // Assuming admin007 is not in mockUsers for this test.
      fireEvent.click(deleteButtons[1]); 

      expect(window.confirm).toHaveBeenCalledWith('Are you sure you want to delete this user? This action cannot be undone.');
      
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith(`/api/admin/users/${mockUsers[1].id}`, {
          method: 'DELETE',
        });
      });
      // Add assertion for users list refresh
      await waitFor(() => expect(global.fetch).toHaveBeenCalledTimes(3)); // Initial, DELETE, Refresh
    });

    it('displays error state if fetching users fails', async () => {
      const fetchErrorMsg = 'Failed to fetch users';
      (global.fetch as jest.Mock).mockRejectedValueOnce(new Error(fetchErrorMsg));
      renderAdminPage();
      
      await waitFor(() => expect(screen.queryByText('Loading user data...')).not.toBeInTheDocument());
      expect(await screen.findByText(`Error: ${fetchErrorMsg}`)).toBeInTheDocument();
    });
  });

  describe('Non-Admin Access', () => {
    it('shows access denied for non-admin user', async () => {
      (useSession as jest.Mock).mockReturnValue({
        data: { user: { id: 'user123', role: 'user', name: 'Normal User' } },
        status: 'authenticated',
      });
      renderAdminPage();
      
      // Loading state might appear briefly
      await waitFor(() => expect(screen.queryByText('Loading user data...')).not.toBeInTheDocument(), { timeout: 2000 });
      expect(await screen.findByText('Access Denied: You do not have permission to view this page.')).toBeInTheDocument();
      expect(global.fetch).not.toHaveBeenCalled(); // Fetch shouldn't be called for non-admins
    });
  });

  describe('Loading State', () => {
    it('shows loading state initially when session is loading', () => {
      (useSession as jest.Mock).mockReturnValue({
        data: null,
        status: 'loading',
      });
      renderAdminPage();
      expect(screen.getByText('Loading user data...')).toBeInTheDocument();
    });
  });
  
  describe('Unauthenticated Access', () => {
    it('shows redirecting message when unauthenticated (fallback)', async () => {
      (useSession as jest.Mock).mockReturnValue({
        data: null,
        status: 'unauthenticated',
      });
      renderAdminPage();
       // Loading state might appear briefly
      await waitFor(() => expect(screen.queryByText('Loading user data...')).not.toBeInTheDocument(), { timeout: 2000 });
      expect(await screen.findByText('Redirecting to login...')).toBeInTheDocument();
    });
  });
});
