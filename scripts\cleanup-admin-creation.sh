#!/bin/bash

# <PERSON><PERSON>t to clean up admin creation files after first admin is created
echo "🧹 Cleaning up admin creation files..."

# Remove the admin creation HTML page
if [ -f "public/create-admin.html" ]; then
    rm "public/create-admin.html"
    echo "✅ Removed public/create-admin.html"
else
    echo "ℹ️  public/create-admin.html not found"
fi

# Remove the admin creation API endpoint
if [ -f "app/api/admin/create-first-admin/route.ts" ]; then
    rm "app/api/admin/create-first-admin/route.ts"
    rmdir "app/api/admin/create-first-admin" 2>/dev/null
    echo "✅ Removed app/api/admin/create-first-admin/route.ts"
else
    echo "ℹ️  app/api/admin/create-first-admin/route.ts not found"
fi

echo ""
echo "🔒 Security cleanup completed!"
echo "📝 Don't forget to:"
echo "   1. Remove ADMIN_CREATION_SECRET from environment variables"
echo "   2. Commit and redeploy your application"
echo "   3. Test admin login functionality"
