import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "../../../../auth/[...nextauth]/route";
import { prismaAuth } from "@/lib/db";

interface RoleUpdateParams {
  params: {
    userId: string;
  };
}

export async function PUT(req: Request, { params }: RoleUpdateParams) {
  const session = await getServerSession(authOptions);

  if (!session || (session.user as any).role !== "admin") {
    return NextResponse.json(
      { message: "Forbidden: Administrator access required." },
      { status: 403 }
    );
  }

  const { userId } = params;
  if (!userId) {
    return NextResponse.json(
      { message: "User ID is required." },
      { status: 400 }
    );
  }

  try {
    const body = await req.json();
    const { role } = body;

    // Validate the role
    if (!role || (role !== "admin" && role !== "user")) {
      return NextResponse.json(
        { message: 'Invalid role specified. Must be "admin" or "user".' },
        { status: 400 }
      );
    }

    // Check if user exists and update role
    const user = await prismaAuth.user.findUnique({
      where: {
        id: userId,
      },
    });

    if (!user) {
      return NextResponse.json({ message: "User not found." }, { status: 404 });
    }

    // Update the user's role in database
    const updatedUser = await prismaAuth.user.update({
      where: {
        id: userId,
      },
      data: {
        role: role,
      },
      select: {
        id: true,
        name: true,
        email: true,
        emailVerified: true,
        image: true,
        role: true,
        createdAt: true,
        updatedAt: true,
        // Exclude hashedPassword for security
      },
    });

    return NextResponse.json(
      { message: "User role updated successfully.", user: updatedUser },
      { status: 200 }
    );
  } catch (error) {
    console.error(`Error updating role for user ${userId}:`, error);
    if (error instanceof SyntaxError) {
      // Handle JSON parsing errors
      return NextResponse.json(
        { message: "Invalid JSON in request body." },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { message: "Error updating user role.", error: (error as Error).message },
      { status: 500 }
    );
  }
}
