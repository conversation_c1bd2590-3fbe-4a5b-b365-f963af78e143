import { NextResponse } from "next/server";
import { prismaAuth } from "@/lib/db";
import bcrypt from "bcrypt";

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { name, email, password, token } = body;

    console.log("Received signup request for:", email); // Log for debugging

    // Validate input
    if (!email || !password) {
      console.log("Missing email or password");
      return NextResponse.json(
        { message: "Email and password are required" },
        { status: 400 }
      );
    }
    if (!name) {
      console.log("Missing name");
      return NextResponse.json(
        { message: "Name is required" },
        { status: 400 }
      );
    }
    if (!token) {
      console.log("Missing invitation token");
      return NextResponse.json(
        { message: "Invitation token is required" },
        { status: 400 }
      );
    }

    // Validate invitation token
    const invitation = await prismaAuth.invitation.findUnique({
      where: { token },
    });

    if (!invitation) {
      console.log("Invalid invitation token:", token);
      return NextResponse.json(
        { message: "Invalid invitation token" },
        { status: 400 }
      );
    }

    if (invitation.isUsed) {
      console.log("Invitation already used:", token);
      return NextResponse.json(
        { message: "Invitation has already been used" },
        { status: 400 }
      );
    }

    if (new Date() > invitation.expiresAt) {
      console.log("Invitation expired:", token);
      return NextResponse.json(
        { message: "Invitation has expired" },
        { status: 400 }
      );
    }

    if (invitation.email !== email) {
      console.log("Email mismatch for invitation:", token);
      return NextResponse.json(
        { message: "Email does not match invitation" },
        { status: 400 }
      );
    }

    // Check if user already exists
    console.log("Checking if user exists with email:", email);
    const existingUser = await prismaAuth.user.findUnique({
      where: {
        email: email,
      },
    });

    if (existingUser) {
      console.log("User already exists with ID:", existingUser.id);
      return NextResponse.json(
        { message: "User already exists with this email" },
        { status: 409 }
      ); // 409 Conflict
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create new user in database with role from invitation
    const newUser = await prismaAuth.user.create({
      data: {
        name,
        email,
        hashedPassword,
        role: invitation.role,
      },
    });

    // Mark invitation as used
    await prismaAuth.invitation.update({
      where: { id: invitation.id },
      data: {
        isUsed: true,
        usedAt: new Date(),
      },
    });

    console.log(
      "Created new user with ID:",
      newUser.id,
      "and role:",
      invitation.role
    );

    // Return success response (excluding sensitive data like hashedPassword)
    const { hashedPassword: _, ...userWithoutPassword } = newUser;
    return NextResponse.json(
      { message: "User created successfully", user: userWithoutPassword },
      { status: 201 }
    );
  } catch (error) {
    console.error("Signup error:", error);
    // Check for specific error types if needed, otherwise generic error
    if (error instanceof Error) {
      return NextResponse.json(
        { message: "Error creating user", error: error.message },
        { status: 500 }
      );
    }
    return NextResponse.json(
      { message: "An unknown error occurred" },
      { status: 500 }
    );
  }
}
