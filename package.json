{"name": "woosync", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@headlessui/react": "^2.2.2", "@heroicons/react": "^2.2.0", "@next-auth/upstash-redis-adapter": "^3.0.4", "@tanstack/react-query": "^5.0.0", "@types/node": "^20.1.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@upstash/redis": "^1.34.8", "bcrypt": "^6.0.0", "chart.js": "^4.4.9", "date-fns": "^4.1.0", "lodash": "^4.17.21", "next": "^13.4.0", "next-auth": "^4.24.11", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "swr": "^2.3.3", "typescript": "^5.0.4"}, "devDependencies": {"@swc/jest": "^0.2.38", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/bcrypt": "^5.0.2", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.16", "autoprefixer": "^10.4.21", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.0"}}