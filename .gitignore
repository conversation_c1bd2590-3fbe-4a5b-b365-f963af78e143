# dependencies
node_modules/

# production
.next/
out/

# testing
coverage/

# environment variables
.env.local
.env.development.local
.env.test.local
.env.production.local

# misc
.DS_Store
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Windows specific
Thumbs.db
ehthumbs.db
Desktop.ini

# Database
*.db
*.db-journal
prisma/dev.db*

# Prisma
/generated/prisma

# Vercel
.vercel

# Build artifacts
dist/
build/

# IDE
.vscode/
.idea/
*.swp
*.swo

# Logs
logs/
*.log
