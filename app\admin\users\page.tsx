"use client";

import { useState, useEffect, ChangeEvent } from "react";
import { useSession } from "next-auth/react";
import { User } from "@/types/user";

type UserForDisplay = Omit<User, "hashedPassword" | "emailVerified"> & {
  emailVerified?: string | null; // Or however you want to display it
};

export default function AdminUsersPage() {
  const { data: session, status } = useSession();
  const [users, setUsers] = useState<UserForDisplay[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State for role management
  const [editingRoleUserId, setEditingRoleUserId] = useState<string | null>(
    null
  );
  const [selectedRole, setSelectedRole] = useState<string>("");

  const fetchUsers = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await fetch("/api/admin/users");
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message || `Failed to fetch users: ${response.statusText}`
        );
      }
      const data: User[] = await response.json();
      // Exclude hashedPassword and format dates if needed
      setUsers(
        data.map((u) => {
          const { hashedPassword, emailVerified, ...rest } = u;
          return {
            ...rest,
            emailVerified: emailVerified?.toString(), // Example formatting
          };
        })
      );
    } catch (err) {
      setError((err as Error).message);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (status === "authenticated" && session?.user?.role === "admin") {
      fetchUsers();
    } else if (status === "authenticated") {
      setError("Access Denied: You do not have permission to view this page.");
      setIsLoading(false);
    }
    // If status is 'loading', we wait. If 'unauthenticated', middleware should redirect.
  }, [status, session]);

  const handleRoleUpdate = async (userId: string) => {
    if (!selectedRole) {
      alert("Please select a role.");
      return;
    }
    try {
      const response = await fetch(`/api/admin/users/${userId}/role`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ role: selectedRole }),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to update role");
      }
      alert("Role updated successfully!");
      setEditingRoleUserId(null); // Close role editing UI
      fetchUsers(); // Refresh user list
    } catch (err) {
      alert(`Error updating role: ${(err as Error).message}`);
    }
  };

  const handleDeleteUser = async (userId: string) => {
    if (
      window.confirm(
        "Are you sure you want to delete this user? This action cannot be undone."
      )
    ) {
      try {
        const response = await fetch(`/api/admin/users/${userId}`, {
          method: "DELETE",
        });
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || "Failed to delete user");
        }
        alert("User deleted successfully!");
        fetchUsers(); // Refresh user list
      } catch (err) {
        alert(`Error deleting user: ${(err as Error).message}`);
      }
    }
  };

  const startEditRole = (user: UserForDisplay) => {
    setEditingRoleUserId(user.id);
    setSelectedRole(user.role);
  };

  if (status === "loading" || isLoading) {
    return (
      <div className="container mx-auto p-4 text-center">
        Loading user data...
      </div>
    );
  }

  if (status === "unauthenticated") {
    // Middleware should handle this, but as a fallback:
    return (
      <div className="container mx-auto p-4 text-center">
        Redirecting to login...
      </div>
    );
  }

  if (session?.user?.role !== "admin") {
    return (
      <div className="container mx-auto p-4 text-center text-red-500">
        Access Denied: You do not have permission to view this page.
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-4 text-red-500">Error: {error}</div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">User Management</h1>
        <div className="flex gap-2">
          <a
            href="/admin/invitations"
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
          >
            Manage Invitations
          </a>
        </div>
      </div>

      <div className="overflow-x-auto bg-white shadow-md rounded-lg">
        <table className="min-w-full table-auto">
          <thead className="bg-gray-100">
            <tr>
              {[
                "ID",
                "Name",
                "Email",
                "Role",
                "Created At",
                "Updated At",
                "Actions",
              ].map((header) => (
                <th
                  key={header}
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  {header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {users.map((user) => (
              <tr key={user.id}>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 truncate max-w-xs">
                  {user.id}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {user.name}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {user.email}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {editingRoleUserId === user.id ? (
                    <select
                      value={selectedRole}
                      onChange={(e: ChangeEvent<HTMLSelectElement>) =>
                        setSelectedRole(e.target.value)
                      }
                      className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                    >
                      <option value="user">User</option>
                      <option value="admin">Admin</option>
                    </select>
                  ) : (
                    user.role
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {new Date(user.createdAt).toLocaleDateString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {new Date(user.updatedAt).toLocaleDateString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  {editingRoleUserId === user.id ? (
                    <>
                      <button
                        onClick={() => handleRoleUpdate(user.id)}
                        className="text-indigo-600 hover:text-indigo-900 mr-3"
                      >
                        Save
                      </button>
                      <button
                        onClick={() => setEditingRoleUserId(null)}
                        className="text-gray-600 hover:text-gray-900"
                      >
                        Cancel
                      </button>
                    </>
                  ) : (
                    <button
                      onClick={() => startEditRole(user)}
                      className="text-indigo-600 hover:text-indigo-900 mr-3"
                    >
                      Edit Role
                    </button>
                  )}
                  <button
                    onClick={() => handleDeleteUser(user.id)}
                    className="text-red-600 hover:text-red-900 ml-3"
                    disabled={session?.user?.id === user.id} // Prevent admin from deleting themselves
                  >
                    Delete
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      {users.length === 0 && !isLoading && (
        <p className="text-center text-gray-500 mt-4">No users found.</p>
      )}
    </div>
  );
}
