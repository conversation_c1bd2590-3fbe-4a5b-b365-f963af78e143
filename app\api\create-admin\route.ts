import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/db";
import bcrypt from "bcrypt";

// Simple admin creation endpoint
export async function POST(req: NextRequest) {
  console.log("Admin creation endpoint called");
  
  try {
    const body = await req.json();
    console.log("Request body received:", { ...body, password: "[HIDDEN]", adminSecret: "[HIDDEN]" });
    
    const { email, password, name, adminSecret } = body;

    // Check for required fields
    if (!email || !password || !name || !adminSecret) {
      console.log("Missing required fields");
      return NextResponse.json(
        { message: "All fields are required" },
        { status: 400 }
      );
    }

    // Check admin secret
    if (adminSecret !== process.env.ADMIN_CREATION_SECRET) {
      console.log("Invalid admin secret");
      return NextResponse.json(
        { message: "Invalid admin secret" },
        { status: 401 }
      );
    }

    // Check if admin already exists
    const existingAdmin = await prisma.user.findFirst({
      where: { role: "admin" },
    });

    if (existingAdmin) {
      console.log("Admin already exists");
      return NextResponse.json(
        { message: "Admin user already exists" },
        { status: 409 }
      );
    }

    // Check if email is already taken
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      console.log("Email already in use");
      return NextResponse.json(
        { message: "Email already in use" },
        { status: 409 }
      );
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create admin user
    const adminUser = await prisma.user.create({
      data: {
        name,
        email,
        hashedPassword,
        role: "admin",
        preferences: JSON.stringify({ itemsPerPage: 10 }),
      },
    });

    console.log("Admin user created successfully:", adminUser.email);

    return NextResponse.json(
      {
        message: "Admin user created successfully",
        user: {
          id: adminUser.id,
          email: adminUser.email,
          name: adminUser.name,
          role: adminUser.role,
        },
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating admin user:", error);
    return NextResponse.json(
      { message: "Internal server error", error: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    );
  }
}

// Add GET method for testing
export async function GET() {
  return NextResponse.json(
    { message: "Admin creation endpoint is available. Use POST method." },
    { status: 200 }
  );
}
