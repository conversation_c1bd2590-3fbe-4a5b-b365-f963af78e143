# Deployment Guide

## Vercel Deployment

### Prerequisites
1. Vercel account
2. GitHub repository
3. Environment variables configured

### Environment Variables
Set these in your Vercel dashboard:

```bash
# Required
DATABASE_URL="your-production-database-url"
NEXTAUTH_SECRET="your-super-secret-key-change-this"
NEXTAUTH_URL="https://your-domain.vercel.app"

# Optional (for WooCommerce integration)
WOOCOMMERCE_URL="https://your-store.com"
WOOCOMMERCE_CONSUMER_KEY="your-consumer-key"
WOOCOMMERCE_CONSUMER_SECRET="your-consumer-secret"
```

### Database Setup
For production, consider using:
- **PostgreSQL**: Vercel Postgres, Supabase, or PlanetScale
- **MySQL**: PlanetScale or Railway
- **SQLite**: Works for small deployments (current setup)

### Deployment Steps
1. Connect your GitHub repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy - Vercel will automatically run the build process
4. After deployment, create an admin user using the API or database directly

### Build Configuration
The project includes:
- `prisma generate` in build script
- `postinstall` hook for Prisma
- Vercel-specific binary targets
- Production logging configuration

### Creating Admin User in Production
After deployment, you can create an admin user by:
1. Using the database directly
2. Temporarily enabling public signup
3. Using the API endpoint with proper authentication

### Troubleshooting
- Ensure `DATABASE_URL` is properly set
- Check that `NEXTAUTH_SECRET` is a secure random string
- Verify `NEXTAUTH_URL` matches your domain
- Check Vercel function logs for errors
