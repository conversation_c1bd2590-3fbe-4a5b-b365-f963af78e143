import { POST } from './route'; // Adjust the path based on your actual file structure
import { Redis } from '@upstash/redis';
import bcrypt from 'bcrypt';
import { NextResponse } from 'next/server';
import { randomUUID } from 'crypto';

// Mock Upstash Redis
const mockRedisGet = jest.fn();
const mockRedisSet = jest.fn();
const mockRedisMulti = jest.fn().mockReturnThis(); // For chaining
const mockRedisExec = jest.fn();

jest.mock('@upstash/redis', () => {
  return {
    Redis: jest.fn(() => ({
      get: mockRedisGet,
      set: mockRedisSet,
      multi: mockRedisMulti,
      exec: mockRedisExec,
    })),
  };
});

// Mock bcrypt
const mockBcryptHash = jest.fn();
jest.mock('bcrypt', () => ({
  hash: mockBcryptHash,
  compare: jest.fn(), // Not used in signup, but good to have if other auth routes are tested
}));

// Mock crypto.randomUUID
const mockRandomUUID = jest.fn();
jest.mock('crypto', () => ({
  ...jest.requireActual('crypto'), // Import and retain default behavior
  randomUUID: () => mockRandomUUID(),
}));


describe('POST /api/auth/signup', () => {
  beforeEach(() => {
    // Clear all mock implementations and call history before each test
    mockRedisGet.mockReset();
    mockRedisSet.mockReset();
    mockRedisMulti.mockClear(); // mockReturnThis means it doesn't need reset for its return
    mockRedisExec.mockReset();
    mockBcryptHash.mockReset();
    mockRandomUUID.mockReset();

    // Default mock implementations
    mockBcryptHash.mockResolvedValue('hashedTestPassword');
    mockRandomUUID.mockReturnValue('test-uuid-123');
  });

  it('should successfully sign up a new user', async () => {
    mockRedisGet.mockResolvedValue(null); // User does not exist
    mockRedisExec.mockResolvedValue(['OK', 'OK']); // Simulate successful transaction

    const requestBody = { name: 'Test User', email: '<EMAIL>', password: 'password123' };
    const req = new Request('http://localhost/api/auth/signup', {
      method: 'POST',
      body: JSON.stringify(requestBody),
      headers: { 'Content-Type': 'application/json' },
    });

    const response = await POST(req);
    const responseBody = await response.json();

    expect(response.status).toBe(201);
    expect(responseBody.message).toBe('User created successfully');
    expect(responseBody.user).toBeDefined();
    expect(responseBody.user.email).toBe(requestBody.email);
    expect(responseBody.user.name).toBe(requestBody.name);
    expect(responseBody.user.id).toBe('test-uuid-123');
    expect(responseBody.user.hashedPassword).toBeUndefined(); // Ensure password is not returned

    expect(mockBcryptHash).toHaveBeenCalledWith(requestBody.password, 10);
    
    expect(mockRedisMulti).toHaveBeenCalledTimes(1);
    // Check the arguments of the chained 'set' calls within the transaction
    // This is a bit tricky as 'set' is part of the chained 'multi' object.
    // We can check the final 'exec' if individual 'set' calls are hard to spy on directly.
    // For more directness, you could modify the mock to capture 'set' args if needed.
    // Here, we'll infer from the structure that 'multi' was called, then 'exec'.
    // The actual user object and email key would be set.
    expect(mockRedisExec).toHaveBeenCalledTimes(1); 
    // To verify the actual set calls, we would need to inspect the pipeline.
    // This mock setup doesn't easily allow that, but we know the transaction was attempted.
  });

  it('should return 409 if user already exists', async () => {
    mockRedisGet.mockResolvedValue('existing-user-id'); // User exists

    const requestBody = { name: 'Test User', email: '<EMAIL>', password: 'password123' };
    const req = new Request('http://localhost/api/auth/signup', {
      method: 'POST',
      body: JSON.stringify(requestBody),
      headers: { 'Content-Type': 'application/json' },
    });

    const response = await POST(req);
    const responseBody = await response.json();

    expect(response.status).toBe(409);
    expect(responseBody.message).toBe('User already exists with this email');
  });

  it('should return 400 for missing email', async () => {
    const requestBody = { name: 'Test User', password: 'password123' }; // Missing email
    const req = new Request('http://localhost/api/auth/signup', {
      method: 'POST',
      body: JSON.stringify(requestBody),
      headers: { 'Content-Type': 'application/json' },
    });

    const response = await POST(req);
    const responseBody = await response.json();

    expect(response.status).toBe(400);
    expect(responseBody.message).toBe('Email and password are required');
  });

  it('should return 400 for missing password', async () => {
    const requestBody = { name: 'Test User', email: '<EMAIL>' }; // Missing password
    const req = new Request('http://localhost/api/auth/signup', {
      method: 'POST',
      body: JSON.stringify(requestBody),
      headers: { 'Content-Type': 'application/json' },
    });

    const response = await POST(req);
    const responseBody = await response.json();

    expect(response.status).toBe(400);
    expect(responseBody.message).toBe('Email and password are required');
  });
  
  it('should return 400 for missing name', async () => {
    const requestBody = { email: '<EMAIL>', password: 'password123' }; // Missing name
    const req = new Request('http://localhost/api/auth/signup', {
      method: 'POST',
      body: JSON.stringify(requestBody),
      headers: { 'Content-Type': 'application/json' },
    });

    const response = await POST(req);
    const responseBody = await response.json();

    expect(response.status).toBe(400);
    expect(responseBody.message).toBe('Name is required');
  });
});
