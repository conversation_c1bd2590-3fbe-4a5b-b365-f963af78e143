import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "../../auth/[...nextauth]/route";
import { prismaAuth } from "@/lib/db";

// GET - Get user preferences
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const user = await prismaAuth.user.findUnique({
      where: { id: session.user.id },
      select: { preferences: true },
    });

    if (!user) {
      return NextResponse.json({ message: "User not found" }, { status: 404 });
    }

    // Parse preferences JSON or return default
    let preferences = { itemsPerPage: 10 };
    if (user.preferences) {
      try {
        preferences = JSON.parse(user.preferences);
      } catch (error) {
        console.error("Error parsing user preferences:", error);
      }
    }

    return NextResponse.json({ preferences }, { status: 200 });
  } catch (error) {
    console.error("Error fetching user preferences:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// PUT - Update user preferences
export async function PUT(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const { preferences } = await request.json();

    if (!preferences || typeof preferences !== "object") {
      return NextResponse.json(
        { message: "Invalid preferences data" },
        { status: 400 }
      );
    }

    // Validate itemsPerPage if provided
    if (preferences.itemsPerPage !== undefined) {
      const itemsPerPage = Number(preferences.itemsPerPage);
      if (
        !Number.isInteger(itemsPerPage) ||
        itemsPerPage < 5 ||
        itemsPerPage > 100
      ) {
        return NextResponse.json(
          { message: "Items per page must be between 5 and 100" },
          { status: 400 }
        );
      }
    }

    // Get current preferences and merge with new ones
    const currentUser = await prismaAuth.user.findUnique({
      where: { id: session.user.id },
      select: { preferences: true },
    });

    let currentPreferences = { itemsPerPage: 10 };
    if (currentUser?.preferences) {
      try {
        currentPreferences = JSON.parse(currentUser.preferences);
      } catch (error) {
        console.error("Error parsing current preferences:", error);
      }
    }

    // Merge preferences
    const updatedPreferences = { ...currentPreferences, ...preferences };

    // Update user preferences
    const updatedUser = await prismaAuth.user.update({
      where: { id: session.user.id },
      data: {
        preferences: JSON.stringify(updatedPreferences),
      },
      select: { preferences: true },
    });

    return NextResponse.json(
      {
        message: "Preferences updated successfully",
        preferences: JSON.parse(updatedUser.preferences || "{}"),
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error updating user preferences:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
