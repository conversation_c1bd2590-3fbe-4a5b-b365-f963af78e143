import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "../../../auth/[...nextauth]/route";
import { Redis } from "@upstash/redis";
import { User } from "@/types/user";

// Initialize Upstash Redis Client
const redis = new Redis({
  url: process.env.UPSTASH_REDIS_URL!,
  token: process.env.UPSTASH_REDIS_TOKEN!,
});

interface DeleteUserParams {
  params: {
    userId: string;
  };
}

export async function DELETE(req: Request, { params }: DeleteUserParams) {
  const session = await getServerSession(authOptions);

  if (!session || (session.user as any).role !== "admin") {
    return NextResponse.json(
      { message: "Forbidden: Administrator access required." },
      { status: 403 }
    );
  }

  const { userId } = params;
  if (!userId) {
    return NextResponse.json(
      { message: "User ID is required." },
      { status: 400 }
    );
  }

  try {
    const userKey = `user:${userId}`;
    const user = await redis.get<User>(userKey);

    if (!user) {
      return NextResponse.json({ message: "User not found." }, { status: 404 });
    }

    // Prepare a pipeline for atomic deletion
    const pipeline = redis.multi();

    // 1. Delete the main user object
    pipeline.del(userKey);

    // 2. Delete the email-to-ID mapping
    if (user.email) {
      const emailKey = `user:email:${user.email}`;
      pipeline.del(emailKey);
    }

    // 3. (Optional but good practice) Delete any NextAuth.js specific session records for this user.
    // The UpstashRedisAdapter stores sessions with keys like `session:${sessionToken}`
    // and also might link user to sessions via `user:session:${userId}` or by storing session tokens in the user object.
    // For UpstashRedisAdapter, it stores session tokens in a list at `user:session:${userId}`.
    const userSessionsKey = `user:session:${userId}`; // As per UpstashRedisAdapter's typical structure
    const sessionTokens = await redis.lrange(userSessionsKey, 0, -1);
    if (sessionTokens && sessionTokens.length > 0) {
      const sessionKeysToDelete = sessionTokens.map(
        (token) => `session:${token}`
      );
      pipeline.del(...sessionKeysToDelete); // Delete individual session objects
    }
    pipeline.del(userSessionsKey); // Delete the list of user's sessions

    // 4. (Optional) If users are part of a global set (e.g., 'users'), remove them.
    // Example: pipeline.srem('users', userId);
    // This depends on the exact storage strategy, which we haven't explicitly defined for a global set.
    // For now, we'll focus on the user object, email mapping, and their sessions.

    await pipeline.exec();

    return NextResponse.json(
      { message: `User ${userId} and associated data deleted successfully.` },
      { status: 200 }
    );
  } catch (error) {
    console.error(`Error deleting user ${userId}:`, error);
    return NextResponse.json(
      { message: "Error deleting user.", error: (error as Error).message },
      { status: 500 }
    );
  }
}
