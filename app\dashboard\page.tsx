"use client";

import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useSearchParams } from "next/navigation";
import FilterBar from "../components/FilterBar";
import Pagination from "../components/Pagination";
import ProductTable from "../components/ProductTable";
import { Product, ProductFilter, ProductsResponse } from "../types/index";
import { fetchProducts } from "../utils/api-client";
import { useLanguage } from "../context/LanguageContext";

const ITEMS_PER_PAGE = 10;

export default function DashboardPage() {
  const { t } = useLanguage();
  const searchParams = useSearchParams();

  // Get current page from URL or default to 1
  const currentPage = Number(searchParams.get("page")) || 1;

  // Initialize filters from URL parameters
  const [filters, setFilters] = useState<ProductFilter>({
    search: searchParams.get("search") || "",
    category: searchParams.get("category") || "",
    stockStatus:
      (searchParams.get("stock_status") as ProductFilter["stockStatus"]) ||
      "all",
  });

  const {
    data: productsData,
    isLoading,
    error,
    isFetching,
    refetch,
  } = useQuery<ProductsResponse, Error>({
    queryKey: [
      "products",
      currentPage,
      filters.search,
      filters.stockStatus,
      filters.category,
    ],
    queryFn: () =>
      fetchProducts({
        search: filters.search,
        page: currentPage,
        per_page: ITEMS_PER_PAGE,
        stockStatus: filters.stockStatus,
        category: filters.category,
      }),
  });

  const products = productsData?.data ?? [];
  const totalItems = productsData?.total ?? 0;
  const totalPages = Math.ceil(totalItems / ITEMS_PER_PAGE);

  return (
    <div className="p-6 max-w-7xl mx-auto space-y-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">{t("dashboard.title")}</h1>
      </div>

      <FilterBar filters={filters} onFilterChange={setFilters} />

      <div className="space-y-4">
        <div className="bg-white rounded-lg shadow">
          {(isLoading || isFetching) && (
            <div className="p-6 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
              <p className="mt-2">{t("dashboard.loading")}</p>
            </div>
          )}

          {!isLoading && !isFetching && error && (
            <div className="p-6 text-center text-red-600 bg-red-50">
              <p>
                {t("dashboard.error")}: {error.message}
              </p>
              <button
                onClick={() => refetch()}
                className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
              >
                {t("dashboard.retry")}
              </button>
            </div>
          )}

          {!isLoading && !isFetching && !error && products.length === 0 && (
            <div className="p-6 text-center text-gray-500">
              <p>{t("dashboard.noProducts")}</p>
            </div>
          )}

          {!isLoading && !error && products.length > 0 && (
            <ProductTable
              products={products}
              onProductUpdate={() => refetch()}
            />
          )}
        </div>

        {totalPages > 1 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={totalItems}
            itemsPerPage={ITEMS_PER_PAGE}
            isLoading={isLoading || isFetching}
          />
        )}
      </div>
    </div>
  );
}
