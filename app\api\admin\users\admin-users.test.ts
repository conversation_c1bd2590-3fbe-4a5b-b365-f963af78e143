import { GET } from './route'; // Adjust path to your actual GET handler
import { Redis } from '@upstash/redis';
import { getServerSession } from 'next-auth/next';
import { NextResponse } from 'next/server';

// Mock Upstash Redis
const mockRedisScan = jest.fn();
const mockRedisPipeline = jest.fn().mockReturnThis();
const mockRedisPipelineGet = jest.fn(); // To be chained from pipeline
const mockRedisPipelineExec = jest.fn();

jest.mock('@upstash/redis', () => {
  return {
    Redis: jest.fn(() => ({
      scan: mockRedisScan,
      pipeline: jest.fn(() => ({ // Mock the pipeline object itself
        get: mockRedisPipelineGet,
        exec: mockRedisPipelineExec,
      })),
      // get: jest.fn(), // If individual gets are used outside pipeline
    })),
  };
});

// Mock next-auth getServerSession
const mockGetServerSession = jest.fn();
jest.mock('next-auth/next', () => ({
  getServerSession: jest.fn((...args) => mockGetServerSession(...args)),
}));

describe('GET /api/admin/users', () => {
  beforeEach(() => {
    mockGetServerSession.mockReset();
    mockRedisScan.mockReset();
    mockRedisPipelineGet.mockReset();
    mockRedisPipelineExec.mockReset();
  });

  it('should return 403 if user is not authenticated', async () => {
    mockGetServerSession.mockResolvedValue(null); // No session

    const req = new Request('http://localhost/api/admin/users');
    const response = await GET(req);
    const responseBody = await response.json();

    expect(response.status).toBe(403);
    expect(responseBody.message).toBe('Forbidden: Administrator access required.');
  });

  it('should return 403 if user is not an admin', async () => {
    mockGetServerSession.mockResolvedValue({ user: { role: 'user', id: 'user1' } }); // Non-admin user

    const req = new Request('http://localhost/api/admin/users');
    const response = await GET(req);
    const responseBody = await response.json();

    expect(response.status).toBe(403);
    expect(responseBody.message).toBe('Forbidden: Administrator access required.');
  });

  it('should successfully fetch users for an admin', async () => {
    mockGetServerSession.mockResolvedValue({ user: { role: 'admin', id: 'admin1' } });

    // Simulate Redis SCAN returning user keys in two batches
    mockRedisScan
      .mockResolvedValueOnce(['cursor-1', ['user:id1', 'user:id2', 'user:email:<EMAIL>']]) // First call
      .mockResolvedValueOnce(['0', ['user:id3']]); // Second call, cursor '0' means done

    // Simulate Redis Pipeline GET and EXEC
    const mockUsers = [
      { id: 'id1', name: 'User One', email: '<EMAIL>', hashedPassword: 'p1', role: 'user', createdAt: new Date(), updatedAt: new Date() },
      { id: 'id2', name: 'User Two', email: '<EMAIL>', hashedPassword: 'p2', role: 'user', createdAt: new Date(), updatedAt: new Date() },
      { id: 'id3', name: 'User Three', email: '<EMAIL>', hashedPassword: 'p3', role: 'admin', createdAt: new Date(), updatedAt: new Date() },
    ];
    // For the first scan (id1, id2 after filtering)
    mockRedisPipelineExec.mockResolvedValueOnce([mockUsers[0], mockUsers[1]]);
    // For the second scan (id3 after filtering)
    mockRedisPipelineExec.mockResolvedValueOnce([mockUsers[2]]);


    const req = new Request('http://localhost/api/admin/users');
    const response = await GET(req);
    const responseBody = await response.json();

    expect(response.status).toBe(200);
    expect(responseBody).toHaveLength(3);
    expect(responseBody[0].name).toBe('User One');
    expect(responseBody[0].hashedPassword).toBeUndefined();
    expect(responseBody[1].name).toBe('User Two');
    expect(responseBody[2].name).toBe('User Three');
    expect(responseBody[2].role).toBe('admin');

    expect(mockRedisScan).toHaveBeenCalledTimes(2);
    expect(mockRedisScan).toHaveBeenCalledWith('0', { match: 'user:*', count: 100 });
    expect(mockRedisScan).toHaveBeenCalledWith('cursor-1', { match: 'user:*', count: 100 });
    
    // Check that pipeline was called for each batch of valid keys
    expect(mockRedisPipelineGet).toHaveBeenCalledWith('user:id1');
    expect(mockRedisPipelineGet).toHaveBeenCalledWith('user:id2');
    expect(mockRedisPipelineGet).toHaveBeenCalledWith('user:id3');
    expect(mockRedisPipelineExec).toHaveBeenCalledTimes(2); // Once for each batch of users fetched via pipeline
  });


  it('should handle empty user list correctly', async () => {
    mockGetServerSession.mockResolvedValue({ user: { role: 'admin', id: 'admin1' } });
    mockRedisScan.mockResolvedValueOnce(['0', []]); // No keys found

    const req = new Request('http://localhost/api/admin/users');
    const response = await GET(req);
    const responseBody = await response.json();

    expect(response.status).toBe(200);
    expect(responseBody).toEqual([]);
    expect(mockRedisScan).toHaveBeenCalledTimes(1);
    expect(mockRedisPipelineExec).not.toHaveBeenCalled(); // Pipeline shouldn't exec if no keys
  });
});
