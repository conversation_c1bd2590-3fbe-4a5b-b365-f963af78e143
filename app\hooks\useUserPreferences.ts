import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";

export interface UserPreferences {
  itemsPerPage: number;
}

const DEFAULT_PREFERENCES: UserPreferences = {
  itemsPerPage: 10,
};

export function useUserPreferences() {
  const { data: session, status } = useSession();
  const [preferences, setPreferences] = useState<UserPreferences>(DEFAULT_PREFERENCES);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load preferences when user is authenticated
  useEffect(() => {
    const loadPreferences = async () => {
      if (status === "authenticated" && session?.user) {
        try {
          setIsLoading(true);
          setError(null);

          const response = await fetch("/api/user/preferences");
          
          if (!response.ok) {
            throw new Error("Failed to load preferences");
          }

          const data = await response.json();
          setPreferences(data.preferences || DEFAULT_PREFERENCES);
        } catch (err) {
          console.error("Error loading preferences:", err);
          setError((err as Error).message);
          // Keep default preferences on error
          setPreferences(DEFAULT_PREFERENCES);
        } finally {
          setIsLoading(false);
        }
      } else if (status === "unauthenticated") {
        // Use default preferences for unauthenticated users
        setPreferences(DEFAULT_PREFERENCES);
        setIsLoading(false);
      }
    };

    loadPreferences();
  }, [session, status]);

  // Update preferences
  const updatePreferences = async (newPreferences: Partial<UserPreferences>) => {
    if (status !== "authenticated" || !session?.user) {
      console.warn("Cannot update preferences: user not authenticated");
      return false;
    }

    try {
      setError(null);

      const response = await fetch("/api/user/preferences", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ preferences: newPreferences }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to update preferences");
      }

      const data = await response.json();
      setPreferences(data.preferences);
      return true;
    } catch (err) {
      console.error("Error updating preferences:", err);
      setError((err as Error).message);
      return false;
    }
  };

  // Update specific preference
  const updateItemsPerPage = async (itemsPerPage: number) => {
    return updatePreferences({ itemsPerPage });
  };

  return {
    preferences,
    isLoading,
    error,
    updatePreferences,
    updateItemsPerPage,
  };
}
