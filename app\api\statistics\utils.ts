import { Redis } from "@upstash/redis";

const redis = new Redis({
  url: process.env.REDIS_URL!,
  token: process.env.REDIS_TOKEN!,
});

async function getCachedData<T>(key: string): Promise<T | null> {
  try {
    const cached = await redis.get(key);
    return cached as T;
  } catch (error) {
    console.error("Redis error:", error);
    return null;
  }
}

async function setCachedData(
  key: string,
  data: any,
  ttl: number
): Promise<void> {
  try {
    await redis.set(key, data, { ex: ttl });
  } catch (error) {
    console.error("Redis error:", error);
  }
}

interface WooCommerceParams {
  [key: string]: string;
}

const CACHE_DURATION = 30 * 60 * 1000; // Increase to 30 minutes
const apiCache = new Map<string, { data: any; timestamp: number }>();

// Rate limiting configuration
const INITIAL_BACKOFF = 1000; // 1 second
const MAX_BACKOFF = 32000; // 32 seconds
const MAX_RETRIES = 3;

// Queue for managing requests
class RequestQueue {
  private queue: (() => Promise<any>)[] = [];
  private processing = false;
  private lastRequestTime = 0;
  private readonly minRequestInterval = 100; // Minimum time between requests (ms)

  async add<T>(request: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.queue.push(async () => {
        try {
          const result = await request();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });
      this.process();
    });
  }

  private async process() {
    if (this.processing) return;
    this.processing = true;

    while (this.queue.length > 0) {
      const now = Date.now();
      const timeToWait = Math.max(
        0,
        this.minRequestInterval - (now - this.lastRequestTime)
      );

      if (timeToWait > 0) {
        await new Promise((resolve) => setTimeout(resolve, timeToWait));
      }

      const request = this.queue.shift();
      if (request) {
        this.lastRequestTime = Date.now();
        await request();
      }
    }

    this.processing = false;
  }
}

const requestQueue = new RequestQueue();

async function fetchWithRetry(
  url: string,
  options: RequestInit,
  retries = MAX_RETRIES
): Promise<Response> {
  let lastError: Error | null = null;
  let backoff = INITIAL_BACKOFF;

  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      const response = await fetch(url, options);

      if (response.status === 429) {
        // Get retry-after header or use exponential backoff
        const retryAfter = response.headers.get("Retry-After");
        const waitTime = retryAfter ? parseInt(retryAfter) * 1000 : backoff;
        await new Promise((resolve) => setTimeout(resolve, waitTime));
        backoff = Math.min(backoff * 2, MAX_BACKOFF);
        continue;
      }

      return response;
    } catch (error) {
      lastError = error as Error;
      if (attempt === retries) break;
      await new Promise((resolve) => setTimeout(resolve, backoff));
      backoff = Math.min(backoff * 2, MAX_BACKOFF);
    }
  }

  throw lastError || new Error("Maximum retries exceeded");
}

export async function fetchWooCommerceData<T>(
  endpoint: string,
  params: WooCommerceParams = {}
): Promise<T> {
  const cacheKey = `${endpoint}-${JSON.stringify(params)}`;
  const cached = apiCache.get(cacheKey);

  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.data as T;
  }

  const batchKey = `batch-${cacheKey}`;
  const existingPromise = apiCache.get(batchKey);
  if (existingPromise) {
    return existingPromise.data;
  }

  const promise = requestQueue.add(async () => {
    if (
      !process.env.WC_CONSUMER_KEY ||
      !process.env.WC_CONSUMER_SECRET ||
      !process.env.NEXT_PUBLIC_WC_API_URL
    ) {
      throw new Error("WooCommerce API credentials not configured");
    }

    const auth = Buffer.from(
      `${process.env.WC_CONSUMER_KEY}:${process.env.WC_CONSUMER_SECRET}`
    ).toString("base64");

    const queryString = new URLSearchParams(params).toString();
    const url = `${process.env.NEXT_PUBLIC_WC_API_URL}/${endpoint}${
      queryString ? `?${queryString}` : ""
    }`;

    try {
      const response = await fetchWithRetry(url, {
        headers: {
          Authorization: `Basic ${auth}`,
        },
        next: { revalidate: CACHE_DURATION / 1000 },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      apiCache.set(cacheKey, {
        data,
        timestamp: Date.now(),
      });

      return data as T;
    } catch (error) {
      console.error(`Error fetching WooCommerce data for ${endpoint}:`, error);
      throw error;
    }
  });

  apiCache.set(batchKey, {
    data: promise,
    timestamp: Date.now(),
  });

  promise.finally(() => {
    apiCache.delete(batchKey);
  });

  return promise;
}
